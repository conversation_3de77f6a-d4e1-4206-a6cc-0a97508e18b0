# LightQuant Design System

## Overview
LightQuant is a sophisticated AI-powered crypto trading platform with a premium, finance-inspired aesthetic. The design emphasizes trust, innovation, and technological sophistication through a carefully crafted visual language that combines cutting-edge technology with financial professionalism.

## Core Design Principles
- **Sophistication**: Premium, finance-inspired aesthetic with glassmorphism effects
- **Innovation**: Cutting-edge AI and technology focus with dynamic animations
- **Trust**: Professional, secure, and reliable appearance
- **Clarity**: Clean layouts with strategic use of whitespace over excessive UI decoration
- **Consistency**: Unified design system across all components and pages
- **Performance**: Smooth animations and interactions that enhance user experience

## Color Palette

### Primary Colors (Black/White Theme)
- **Pure Black**: `#000000` - Primary background, represents sophistication and premium feel
- **Pure White**: `#FFFFFF` - Primary text color, clean contrast
- **Gray-900**: `#111827` - Secondary background for sections
- **Gray-800**: `#1F2937` - Card backgrounds, subtle contrast
- **Gray-700**: `#374151` - Borders, dividers
- **Gray-600**: `#4B5563` - Muted borders
- **Gray-500**: `#6B7280` - Supporting elements
- **Gray-400**: `#9CA3AF` - Muted text
- **Gray-300**: `#D1D5DB` - Light text
- **Gray-200**: `#E5E7EB` - Very light text
- **Gray-100**: `#F3F4F6` - Subtle accents

### Accent Colors (Blue/Purple Highlights)
- **Primary Blue**: `#228BFF` - Main brand color, trust, technology (as specified in memory)
- **Blue-600**: `#2563EB` - Darker blue for buttons
- **Blue-500**: `#3B82F6` - Standard blue
- **Blue-400**: `#60A5FA` - Light blue for text
- **Cyan-500**: `#06B6D4` - Secondary accent, innovation, freshness
- **Cyan-400**: `#22D3EE` - Light cyan
- **Purple-600**: `#9333EA` - Deep purple
- **Purple-500**: `#8B5CF6` - Standard purple, premium, AI/tech
- **Purple-400**: `#A78BFA` - Light purple

### Supporting Colors
- **Amber-500**: `#F59E0B` - Success, performance, premium
- **Amber-400**: `#FBBF24` - Light amber
- **Orange-500**: `#F97316` - Warning, energy
- **Green-500**: `#10B981` - Success, positive performance
- **Green-400**: `#34D399` - Light green
- **Red-500**: `#EF4444` - Error, losses
- **Pink-500**: `#EC4899` - Accent color for gradients

### Text Hierarchy
- **Primary Text**: `#FFFFFF` - Headings, important content
- **Secondary Text**: `#E5E7EB` - Body text, descriptions
- **Muted Text**: `#9CA3AF` - Supporting text, labels
- **Accent Text**: `#228BFF` - Links, interactive elements
- **Success Text**: `#10B981` - Positive indicators
- **Warning Text**: `#F59E0B` - Caution indicators
- **Error Text**: `#EF4444` - Error states

## Typography (Space Grotesk + Inter)

### Font Families
- **Headings**: Space Grotesk - Modern, geometric, tech-forward (as specified in memory)
- **Body Text**: Inter - Clean, highly readable, professional
- **Fallback**: system-ui, -apple-system, sans-serif

### Font Weights
- **Light**: 300 - Subtle text, taglines
- **Regular**: 400 - Body text
- **Medium**: 500 - Emphasized text
- **Semibold**: 600 - Subheadings
- **Bold**: 700 - Headings
- **Extrabold**: 800 - Important headings
- **Black**: 900 - Hero titles, major headings

### Typography Scale
- **Hero**: 6xl-8xl (96px-128px) - Main hero titles with Space Grotesk
- **H1**: 4xl-5xl (48px-60px) - Page titles with Space Grotesk
- **H2**: 3xl-4xl (36px-48px) - Section titles with Space Grotesk
- **H3**: 2xl-3xl (24px-36px) - Subsection titles with Space Grotesk
- **H4**: xl-2xl (20px-24px) - Card titles with Space Grotesk
- **Body Large**: lg-xl (18px-20px) - Important body text with Inter
- **Body**: base-lg (16px-18px) - Regular body text with Inter
- **Body Small**: sm (14px) - Supporting text with Inter
- **Caption**: xs (12px) - Labels, captions with Inter

### Typography Treatments
- **Gradient Text**: Use `bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent`
- **Animate Gradient**: Add `animate-gradient bg-300` for moving gradients
- **Tracking**: Use `tracking-tight` for headings, `tracking-wide` for emphasis

## Spacing System

### Base Unit: 4px (0.25rem)
- **xs**: 2px (0.125rem)
- **sm**: 4px (0.25rem)
- **1**: 4px (0.25rem)
- **2**: 8px (0.5rem)
- **3**: 12px (0.75rem)
- **4**: 16px (1rem)
- **5**: 20px (1.25rem)
- **6**: 24px (1.5rem)
- **8**: 32px (2rem)
- **10**: 40px (2.5rem)
- **12**: 48px (3rem)
- **16**: 64px (4rem)
- **20**: 80px (5rem)

### Section Spacing
- **Section Padding**: `py-20` (80px) - Standard section vertical padding
- **Container Max Width**: `max-w-7xl` (1280px) - Content container
- **Container Padding**: `px-6` (24px) - Horizontal container padding
- **Component Gap**: `gap-8` (32px) - Standard gap between components
- **Card Padding**: `p-8` (32px) - Standard card internal padding

## Border Radius

### Radius Scale
- **Small**: `rounded-lg` (8px) - Small elements, badges
- **Medium**: `rounded-xl` (12px) - Buttons, inputs
- **Large**: `rounded-2xl` (16px) - Cards, containers
- **Extra Large**: `rounded-3xl` (24px) - Feature cards, major containers

## Shadows & Effects

### Shadow Levels
- **Subtle**: `shadow-sm` - Minimal elevation
- **Standard**: `shadow-lg` - Standard cards
- **Prominent**: `shadow-xl` - Important elements
- **Dramatic**: `shadow-2xl` - Hero elements, modals

### Glow Effects (Finance-Inspired)
- **Blue Glow**: `glow-blue` - `box-shadow: 0 0 20px rgba(34, 139, 255, 0.3)`
- **Gold Glow**: `glow-gold` - `box-shadow: 0 0 20px rgba(251, 191, 36, 0.3)`
- **Premium Glow**: Animated glow with `animate-premium-glow`

### Glassmorphism Effects
- **Backdrop Blur**: `backdrop-blur-xl` - Glass morphism effect
- **Background Opacity**: `bg-black/95`, `bg-gray-900/50` - Translucent backgrounds
- **Border Opacity**: `border-gray-800/50` - Subtle borders

## Gradients (Sophisticated)

### Primary Gradients
- **Hero Gradient**: `from-blue-600 via-blue-500 to-cyan-500`
- **Interactive Gradient**: `from-blue-500 via-purple-500 to-cyan-400`
- **Success Gradient**: `from-green-500 to-emerald-500`
- **Warning Gradient**: `from-amber-500 to-orange-500`
- **Premium Gradient**: `from-purple-500 to-pink-500`

### Background Gradients (Subtle)
- **Ambient**: `from-blue-500/10 via-purple-500/10 to-cyan-500/10`
- **Fade**: `from-gray-900 via-gray-900/50 to-transparent`
- **Grid Pattern**: Custom CSS for matrix-style backgrounds

### Text Gradients
- **Primary**: `from-blue-400 via-purple-400 to-cyan-400`
- **White Gradient**: `from-white via-gray-100 to-white`
- **Animate**: Use `animate-gradient bg-300` for moving gradients

## Animation System (Sophisticated)

### Timing Functions
- **Ease Out**: `ease-out` - Entrances, reveals
- **Ease In Out**: `ease-in-out` - Smooth transitions
- **Linear**: `linear` - Continuous animations

### Duration Scale
- **Fast**: 200ms-300ms - Micro-interactions, hovers
- **Medium**: 500ms - Standard transitions
- **Slow**: 700ms-1000ms - Complex animations, page transitions

### Custom Animations (Finance-Inspired)
- **Fade In Up**: `animate-fade-in-up` - Content entrance
- **Scale In**: `animate-scale-in` - Card entrance
- **Float Complex**: `animate-float-complex` - Sophisticated floating
- **Gradient**: `animate-gradient` - Moving gradient backgrounds
- **Shimmer**: `animate-shimmer` - Button hover effects
- **Glow**: `animate-premium-glow` - Pulsing glow effects
- **Particle Drift**: `animate-particle-drift` - Floating particles
- **Chart Draw**: `animate-chart-draw` - SVG line drawing

### Animation Delays
- **Staggered**: Use `delay-100`, `delay-200`, etc. for sequential reveals
- **Complex**: Up to `delay-1100` for sophisticated sequences

## Component Patterns (Consistent & Reusable)

### Cards (Premium Finance Style)
- **Background**: `bg-black` or `bg-gray-900/50` with glassmorphism
- **Border**: Colored borders with low opacity (`border-blue-500/30`)
- **Radius**: `rounded-3xl` (24px) for premium feel
- **Hover**: Subtle scale, color intensity increase, glow effects
- **Padding**: `p-8` (32px) standard for breathing room
- **Transform**: `hover:-translate-y-2` or `hover:-translate-y-4` for depth

### Buttons (Interactive & Engaging)
- **Primary**: Gradient background (`from-blue-600 via-blue-500 to-cyan-500`)
- **Secondary**: Border style with transparent background
- **Hover**: Scale transform (`hover:scale-105`), glow effects, shimmer
- **Padding**: `px-6 py-3` (medium), `px-10 py-5` (large)
- **Radius**: `rounded-xl` or `rounded-2xl`
- **Effects**: Shimmer animation, premium glow on hover

### Icons (Sophisticated Containers)
- **Container**: Colored background with matching border
- **Size**: `w-16 h-16` (containers), `w-8 h-8` (icons)
- **Colors**: Match accent color scheme (blue, purple, amber, etc.)
- **Hover**: `group-hover:scale-110` with glow effects
- **Background**: Semi-transparent colored backgrounds (`bg-blue-500/10`)

### Navigation (Clean & Professional)
- **Active State**: Blue color (`text-blue-400`) with underline
- **Hover**: Scale underline effect with smooth transitions
- **Mobile**: Slide-down menu with backdrop blur
- **Logo**: White inverted logo with hover effects
- **Scroll**: Background blur and shadow on scroll

## Layout Principles (Finance-Inspired)

### Grid System (Responsive & Clean)
- **Desktop**: 3-4 columns for feature grids
- **Tablet**: 2 columns with proper spacing
- **Mobile**: 1 column, stacked layout with full width

### Whitespace (Generous & Strategic)
- **Generous**: Ample spacing between sections for breathing room
- **Breathing Room**: Proper padding within components
- **Visual Hierarchy**: Spacing to create clear content hierarchy
- **Section Gaps**: `py-20` (80px) between major sections

### Responsive Design (Mobile-First)
- **Mobile First**: Start with mobile, enhance for larger screens
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)
- **Typography**: Scale up from mobile (`text-4xl md:text-6xl`)
- **Spacing**: Increase padding and margins on larger screens

## Interactive States (Sophisticated)

### Hover Effects (Premium Feel)
- **Scale**: `hover:scale-105` - Subtle growth for buttons
- **Transform**: `hover:-translate-y-2` - Lift effect for cards
- **Color**: Intensity increase, glow addition
- **Glow**: Premium glow effects for important elements

### Focus States (Accessible)
- **Outline**: Blue ring for accessibility compliance
- **Glow**: Subtle glow effect matching brand colors
- **Visibility**: Clear focus indicators for keyboard navigation

### Active States (Responsive)
- **Scale**: Slight scale down for pressed state
- **Color**: Darker/more intense colors
- **Feedback**: Immediate visual response to interactions

## Effects & Utilities (Advanced)

### Glassmorphism (Modern & Premium)
- **Background**: `backdrop-blur-xl` with semi-transparent backgrounds
- **Borders**: Low opacity colored borders for depth
- **Layering**: Multiple layers for sophisticated depth

### Glow Effects (Finance-Inspired)
- **Blue Glow**: `glow-blue` utility class for primary elements
- **Gold Glow**: `glow-gold` utility class for success elements
- **Custom**: Box-shadow with colored rgba values
- **Animated**: Pulsing glow effects for dynamic elements

### Grid Patterns (Subtle Tech Feel)
- **Background**: `bg-grid-pattern` utility for subtle grid overlay
- **Matrix**: Animated matrix-style grid patterns
- **Particles**: Floating particle effects for ambiance

## Accessibility (Professional Standards)

### Color Contrast (WCAG Compliant)
- **Text**: Minimum 4.5:1 ratio against background
- **Interactive**: Clear visual feedback for all states
- **Testing**: Regular contrast testing for all color combinations

### Focus Management (Keyboard Navigation)
- **Visible**: Clear focus indicators with brand colors
- **Logical**: Proper tab order throughout interface
- **Skip Links**: For screen reader accessibility

### Motion (Respectful & Purposeful)
- **Respectful**: Consider reduced motion preferences
- **Purposeful**: Animations enhance, don't distract
- **Performance**: Smooth 60fps animations

## Brand Voice in Design (LightQuant Identity)

### Sophisticated (Premium Finance)
- Clean lines, premium materials
- Subtle animations, refined interactions
- Professional color palette
- Generous whitespace usage

### Innovative (Cutting-Edge Technology)
- Cutting-edge visual effects
- Modern typography and layouts
- Dynamic animations and interactions
- Glassmorphism and advanced effects

### Trustworthy (Financial Reliability)
- Consistent patterns across all pages
- Professional color palette
- Clear information hierarchy
- Reliable interaction patterns

## Implementation Guidelines

### Component Reusability
- Use shadcn components as base for consistency
- Create reusable patterns for common elements
- Maintain consistent spacing and sizing
- Follow established color and typography patterns

### Performance Considerations
- Optimize animations for 60fps
- Use CSS transforms for smooth animations
- Minimize layout shifts
- Progressive enhancement for advanced effects

### Consistency Checklist
- [ ] Colors match the defined palette
- [ ] Typography uses Space Grotesk for headings, Inter for body
- [ ] Spacing follows the 4px base unit system
- [ ] Animations use defined timing and easing
- [ ] Components follow established patterns
- [ ] Hover states are consistent across similar elements
- [ ] Focus states are accessible and visible
- [ ] Mobile responsiveness is maintained
