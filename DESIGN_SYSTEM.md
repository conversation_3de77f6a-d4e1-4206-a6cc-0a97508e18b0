# LightQuant Design System

## Overview
LightQuant follows a sophisticated, finance-inspired design system with a dark theme, premium gradients, and advanced animations. The design emphasizes professionalism, trust, and cutting-edge technology.

## Color Palette

### Primary Colors
- **Black**: `#000000` - Primary background
- **White**: `#FFFFFF` - Primary text
- **Gray-900**: `#111827` - Secondary background
- **Gray-800**: `#1F2937` - Card backgrounds
- **Gray-700**: `#374151` - Borders
- **Gray-400**: `#9CA3AF` - Secondary text
- **Gray-300**: `#D1D5DB` - Muted text

### Accent Colors
- **Blue**: `#3B82F6` (#228BFF in memories) - Primary accent
- **Purple**: `#8B5CF6` - Secondary accent
- **Cyan**: `#06B6D4` - Tertiary accent
- **Amber**: `#F59E0B` - Warning/highlight
- **Green**: `#10B981` - Success
- **Pink**: `#EC4899` - Special accent

### Gradient Combinations
- **Primary**: `from-blue-500 to-cyan-500`
- **Secondary**: `from-purple-500 to-pink-500`
- **Tertiary**: `from-amber-500 to-orange-500`
- **Success**: `from-green-500 to-emerald-500`
- **Hero**: `from-blue-600 via-blue-500 to-cyan-500`

## Typography

### Font Family
- **Primary**: Inter (system-ui fallback)
- **Weights**: 300, 400, 500, 600, 700, 800, 900

### Text Hierarchy
- **Hero Title**: `text-6xl md:text-8xl font-black` - Ultra large, bold
- **Page Title**: `text-4xl md:text-6xl font-bold` - Large titles
- **Section Title**: `text-4xl md:text-5xl font-bold` - Section headers
- **Card Title**: `text-2xl font-bold` - Component titles
- **Body Large**: `text-xl` - Important descriptions
- **Body**: `text-base` - Regular content
- **Small**: `text-sm` - Secondary info

### Text Colors
- **Primary**: `text-white` - Main content
- **Secondary**: `text-gray-300` - Important secondary
- **Muted**: `text-gray-400` - Regular secondary
- **Accent**: Gradient text using `bg-gradient-to-r bg-clip-text text-transparent`

## Spacing & Layout

### Container
- **Max Width**: `max-w-7xl mx-auto px-6`
- **Section Padding**: `py-20` (80px vertical)
- **Component Padding**: `p-8` (32px all sides)

### Grid Systems
- **3-Column**: `grid md:grid-cols-3 gap-8`
- **4-Column**: `grid md:grid-cols-2 lg:grid-cols-4 gap-8`
- **2-Column**: `grid lg:grid-cols-2 gap-16`

## Components

### Cards
- **Background**: `bg-black` or `bg-gray-900/50`
- **Border**: `border border-gray-800/50` with colored variants
- **Radius**: `rounded-3xl` (24px)
- **Hover**: `hover:bg-gray-800/50 hover:border-gray-700/70`
- **Transform**: `transform hover:-translate-y-2` or `hover:-translate-y-4`

### Buttons
- **Primary**: Gradient background with hover effects
- **Secondary**: Border with transparent background
- **Sizes**: `px-8 py-4` (large), `px-6 py-3` (medium)
- **Radius**: `rounded-xl` or `rounded-2xl`
- **Effects**: `hover:scale-105` or `hover:scale-110`

### Icons
- **Container**: Colored background with matching border
- **Size**: `w-16 h-16` (containers), `w-8 h-8` (icons)
- **Colors**: Match accent color scheme
- **Hover**: `group-hover:scale-110`

## Animations

### Entrance Animations
- **Fade In Up**: `animate-fade-in-up` with delays
- **Scale In**: `animate-scale-in` for cards
- **Slide In**: `animate-slide-in-left/right` for content

### Hover Effects
- **Transform**: Scale, translate, rotate combinations
- **Glow**: Custom glow effects for premium feel
- **Shimmer**: `animate-shimmer` for buttons

### Background Animations
- **Floating Orbs**: `animate-float-complex` with delays
- **Particles**: `animate-particle-drift` for ambiance
- **Gradients**: `animate-gradient` for text effects

## Patterns

### Section Structure
```
<section className="py-20 bg-[background-color]">
  <div className="max-w-7xl mx-auto px-6">
    <div className="text-center mb-16">
      <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
      <p className="text-xl text-gray-400 max-w-3xl mx-auto">
    </div>
    <!-- Content Grid -->
  </div>
</section>
```

### Card Pattern
```
<div className="group bg-black border border-gray-800/50 rounded-3xl p-8 hover:bg-gray-800/50 transition-all duration-500 transform hover:-translate-y-2">
  <div className="w-16 h-16 bg-[color]/10 border border-[color]/30 rounded-2xl flex items-center justify-center mb-6">
    <Icon className="w-8 h-8 text-[color]" />
  </div>
  <h3 className="text-2xl font-bold text-white mb-4">Title</h3>
  <p className="text-gray-400">Description</p>
</div>
```

## Effects & Utilities

### Glassmorphism
- **Background**: `backdrop-blur-xl` with semi-transparent backgrounds
- **Borders**: Low opacity colored borders

### Glow Effects
- **Blue Glow**: `glow-blue` utility class
- **Gold Glow**: `glow-gold` utility class
- **Custom**: Box-shadow with colored rgba values

### Grid Patterns
- **Background**: `bg-grid-pattern` utility for subtle grid overlay

## Responsive Design

### Breakpoints
- **Mobile**: Default styles
- **Tablet**: `md:` prefix (768px+)
- **Desktop**: `lg:` prefix (1024px+)

### Typography Scaling
- Mobile-first approach with larger sizes on desktop
- Example: `text-4xl md:text-6xl`

## Accessibility

### Focus States
- **Ring**: `focus-visible:ring-1 focus-visible:ring-ring`
- **Outline**: `focus-visible:outline-none`

### Color Contrast
- High contrast maintained between text and backgrounds
- Accent colors provide sufficient contrast ratios

## Brand Voice
- **Professional**: Finance-industry appropriate
- **Cutting-edge**: Technology-forward language
- **Trustworthy**: Emphasis on security and transparency
- **Accessible**: Complex concepts explained simply
