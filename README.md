# Lightquant

This project uses Next.js with the App Router. To run the development server:

```bash
pnpm install
pnpm dev
```

Before running, copy `.env.example` to `.env` and provide the required database, NextAuth and wallet variables.

## Database Setup

Prisma is used for the PostgreSQL schema. After configuring `DATABASE_URL` in your `.env`, generate the client and apply the migrations:

```bash
pnpm db:generate
pnpm db:migrate
```

This will create the necessary tables in your database.
